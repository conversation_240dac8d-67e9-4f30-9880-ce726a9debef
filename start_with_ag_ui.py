#!/usr/bin/python3
# coding: utf-8
"""
统一的AG-UI服务启动脚本
支持完整的LangGraph工作流和CopilotKit集成
"""

import uvicorn
import json
import logging
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="LangGraph AG-UI Service",
    description="LangGraph服务，支持AG-UI协议和CopilotKit集成",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 异常处理
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    return JSONResponse(
        status_code=422,
        content={"detail": exc.errors(), "body": exc.body}
    )

# 健康检查端点
@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "LangGraph AG-UI Service"}

# 根路径
@app.get("/")
async def root():
    return {
        "message": "LangGraph AG-UI Service",
        "endpoints": {
            "health": "/health",
            "ag_ui_websocket": "/ws/ag-ui",
            "ag_ui_websocket_info": "/ws/ag-ui/info",
            "ag_ui_http": "/ag-ui/run",
            "ag_ui_health": "/ag-ui/health",
            "ag_ui_info": "/ag-ui/info",
            "original_websocket": "/ws/chat"
        }
    }

# ============ AG-UI CopilotKit 兼容端点 ============

@app.websocket("/ws/ag-ui")
async def ag_ui_websocket_endpoint(websocket: WebSocket):
    """
    AG-UI兼容的WebSocket端点
    支持CopilotKit协议的实时通信
    """
    await websocket.accept()

    try:
        logger.info("AG-UI WebSocket连接已建立")

        while True:
            try:
                data = await websocket.receive_text()
                request_data = json.loads(data)
                logger.info(f"收到AG-UI请求: {request_data}")

                # 处理不同类型的请求
                request_type = request_data.get("type", "run")

                if request_type == "run":
                    # 处理运行请求 - 这里可以集成完整的LangGraph工作流
                    await _handle_ag_ui_run_request(websocket, request_data)
                elif request_type == "heartbeat":
                    # 处理心跳
                    await websocket.send_text(json.dumps({
                        "type": "heartbeat_ack",
                        "timestamp": request_data.get("timestamp")
                    }))
                else:
                    logger.warning(f"未知的请求类型: {request_type}")
                    await websocket.send_text(json.dumps({
                        "type": "error",
                        "error": f"未知的请求类型: {request_type}"
                    }))

            except json.JSONDecodeError as e:
                logger.error(f"JSON解析错误: {e}")
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "error": "无效的JSON格式"
                }))

    except WebSocketDisconnect:
        logger.info("AG-UI WebSocket连接已断开")
    except Exception as e:
        logger.error(f"AG-UI WebSocket错误: {e}", exc_info=True)
        await websocket.close()

async def _handle_ag_ui_run_request(websocket: WebSocket, request_data: dict):
    """
    处理AG-UI运行请求
    可以在这里集成完整的LangGraph工作流
    """
    try:
        messages = request_data.get("messages", [])
        if not messages:
            await websocket.send_text(json.dumps({
                "type": "error",
                "error": "消息列表不能为空"
            }))
            return

        # 发送开始事件
        await websocket.send_text(json.dumps({
            "type": "run_started",
            "run_id": request_data.get("run_id"),
            "thread_id": request_data.get("thread_id")
        }))

        # 处理消息内容
        user_message = messages[0].get("content", "") if messages else ""

        # 发送文本消息开始
        await websocket.send_text(json.dumps({
            "type": "text_message_start",
            "content": f"正在处理您的请求: {user_message}"
        }))

        # 这里可以集成真实的LangGraph工作流
        # 目前使用简单的回复作为示例
        response_content = f"Hello! 我收到了您的消息: {user_message}"

        # 发送文本消息内容
        await websocket.send_text(json.dumps({
            "type": "text_message_delta",
            "content": response_content
        }))

        # 发送文本消息结束
        await websocket.send_text(json.dumps({
            "type": "text_message_end"
        }))

        # 发送运行结束
        await websocket.send_text(json.dumps({
            "type": "run_finished",
            "status": "completed"
        }))

    except Exception as e:
        logger.error(f"处理AG-UI运行请求失败: {e}", exc_info=True)
        await websocket.send_text(json.dumps({
            "type": "error",
            "error": str(e)
        }))

# ============ CopilotKit 必需的HTTP端点 ============

@app.head("/ws/ag-ui/info")
async def ag_ui_ws_info_head():
    """
    CopilotKit会发送HEAD请求到这个端点来检查服务是否可用
    """
    return JSONResponse(content={}, status_code=200)

@app.get("/ws/ag-ui/info")
async def ag_ui_ws_info_get():
    """
    CopilotKit期望的WebSocket info HTTP端点
    """
    return {
        "name": "LangGraph AG-UI Adapter",
        "description": "LangGraph工作流适配为AG-UI协议的服务",
        "version": "1.0.0",
        "protocol_version": "ag-ui-1.0",
        "endpoints": {
            "websocket": "/ws/ag-ui",
            "http": "/ag-ui/run",
            "health": "/ag-ui/health",
            "info": "/ag-ui/info"
        },
        "supported_events": [
            "run_started",
            "run_finished",
            "text_message_start",
            "text_message_delta",
            "text_message_end",
            "tool_calls_start",
            "tool_calls_end",
            "state_snapshot",
            "error"
        ],
        "features": {
            "streaming": True,
            "state_management": True,
            "tool_calls": True,
            "interrupts": True,
            "multi_agent": True,
            "human_in_loop": True
        }
    }

@app.get("/ag-ui/info")
async def ag_ui_info():
    """
    AG-UI HTTP信息端点
    """
    return {
        "name": "LangGraph AG-UI Adapter",
        "description": "LangGraph工作流适配为AG-UI协议的服务",
        "version": "1.0.0",
        "protocol_version": "ag-ui-1.0",
        "endpoints": {
            "websocket": "/ws/ag-ui",
            "http": "/ag-ui/run",
            "health": "/ag-ui/health",
            "info": "/ag-ui/info"
        },
        "supported_events": [
            "run_started",
            "run_finished",
            "text_message_start",
            "text_message_delta",
            "text_message_end",
            "tool_calls_start",
            "tool_calls_end",
            "state_snapshot",
            "error"
        ],
        "features": {
            "streaming": True,
            "state_management": True,
            "tool_calls": True,
            "interrupts": True,
            "multi_agent": True,
            "human_in_loop": True
        }
    }

@app.get("/ag-ui/health")
async def ag_ui_health_check():
    """
    AG-UI健康检查端点
    """
    return {
        "status": "healthy",
        "service": "langgraph-ag-ui-adapter",
        "version": "1.0.0"
    }

# ============ 原有路由集成 ============

# 尝试导入原有的完整LangGraph路由
try:
    logger.info("尝试导入完整的LangGraph路由...")
    from apps.endpoints import monitor
    from apps.endpoints.llm import api as llm_api

    app.include_router(monitor.router, prefix="/monitor", tags=["monitor"])
    app.include_router(llm_api.router, prefix="/llm", tags=["llm"])

    logger.info("✅ 完整LangGraph路由导入成功")

except Exception as e:
    logger.warning(f"⚠️ 完整路由导入失败: {e}")
    logger.info("回退到基础AG-UI模式...")

    # 如果完整路由导入失败，使用基础的AG-UI功能
    logger.info("✅ 使用基础AG-UI功能模式")

if __name__ == "__main__":
    logger.info("启动统一的LangGraph AG-UI服务...")
    logger.info("服务地址: http://localhost:8080")
    logger.info("AG-UI WebSocket: ws://localhost:8080/ws/ag-ui")
    logger.info("AG-UI WebSocket Info: ws://localhost:8080/ws/ag-ui/info")
    logger.info("AG-UI HTTP Info: http://localhost:8080/ag-ui/info")
    logger.info("健康检查: http://localhost:8080/health")

    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8080,
        log_level="info"
    )
