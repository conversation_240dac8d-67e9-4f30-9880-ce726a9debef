#!/bin/bash

# WebSocket修复测试启动脚本

echo "🚀 开始WebSocket修复测试..."

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装"
    exit 1
fi

# 检查是否在正确的目录
if [ ! -f "main.py" ]; then
    echo "❌ 请在 toolchain-muse-app 目录下运行此脚本"
    exit 1
fi

# 检查依赖
echo "📦 检查依赖..."
python3 -c "import websockets, asyncio, json" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "⚠️ 缺少websockets依赖，尝试安装..."
    pip3 install websockets
fi

# 启动服务器（后台运行）
echo "🔧 启动WebSocket服务器..."
python3 main.py &
SERVER_PID=$!

# 等待服务器启动
echo "⏳ 等待服务器启动（10秒）..."
sleep 10

# 检查服务器是否启动成功
if ! ps -p $SERVER_PID > /dev/null; then
    echo "❌ 服务器启动失败"
    exit 1
fi

echo "✅ 服务器已启动 (PID: $SERVER_PID)"

# 运行测试
echo "🧪 运行WebSocket修复测试..."
python3 test_websocket_fix.py

# 保存测试结果
TEST_RESULT=$?

# 停止服务器
echo "🛑 停止服务器..."
kill $SERVER_PID
wait $SERVER_PID 2>/dev/null

# 输出最终结果
echo ""
echo "=" * 50
if [ $TEST_RESULT -eq 0 ]; then
    echo "🎉 测试完成！请查看上方日志了解详细结果"
else
    echo "❌ 测试过程中出现错误"
fi
echo "=" * 50

exit $TEST_RESULT
