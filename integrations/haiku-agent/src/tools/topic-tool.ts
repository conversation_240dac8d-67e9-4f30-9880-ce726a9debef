import { generateText } from 'ai';
import type { AgentTool } from '@workspace/shared';
import { customOpenAI } from '@workspace/shared';

export class TopicExtractorTool implements AgentTool {
  id = 'topic-extractor';
  name = '主题提取器';
  description = '从用户消息中提取主要主题';

  /**
   * 执行主题提取
   * 添加更严格的输入验证和错误处理
   */
  async execute(input: string): Promise<string> {
    // 验证输入不为空
    if (!input || input.trim().length === 0) {
      console.warn('主题提取器：输入为空，返回默认主题');
      return 'general';
    }

    try {
      const result = await generateText({
        model: customOpenAI('gpt-4o-2024-11-20'),
        prompt: `请从以下用户输入中提取主要主题。只返回主题，不要其他内容。始终用中文回复。\n\n用户输入：${input.trim()}`,
        maxTokens: 50
      });

      // 验证 AI 返回结果
      const extractedTopic = result.text?.trim();
      if (!extractedTopic) {
        console.warn('主题提取器：AI 返回空结果，使用备用方案');
        return input.trim();
      }

      return extractedTopic;
    } catch (error) {
      console.error('主题提取器：AI 主题提取失败，使用备用方案:', error);
      // 返回清理后的原始输入作为备用
      return input.trim() || 'general';
    }
  }
}

// 导出工具实例
export const topicExtractorTool = new TopicExtractorTool();
