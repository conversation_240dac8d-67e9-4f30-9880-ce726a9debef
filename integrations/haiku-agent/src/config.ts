/**
 * Haiku Integration 配置
 * 提供默认配置和配置工厂函数
 * 重要：所有配置都应确保代理始终使用中文回复
 */

import type { AgentConfig } from './types.js';

/**
 * 默认的 Agent 配置
 */
export const DEFAULT_CONFIG: AgentConfig = {
  // 模型配置
  model: 'gpt-4o',
  temperature: 0.7,
  maxTokens: 500,
  systemPrompt:
    '你是一个专业的诗歌生成助手，能够创作优美的中英双语诗歌。重要规则：始终使用中文回复用户，无论用户使用什么语言提问，都必须用中文回复。所有解释、说明和交流都必须使用中文。',

  // 生成配置
  style: 'traditional',
  outputFormat: 'both',
  includeExtras: true,

  // 重试配置
  maxRetries: 3,
  retryDelay: 1000,

  // 初始状态
  initialState: {
    items: [],
    currentItem: undefined,
    selectedIndex: -1,
    topic: '',
    isGenerating: false,
    history: [],
    preferences: {
      style: 'traditional',
      outputFormat: 'both',
      includeExtras: true
    },
    lastError: undefined
  }
};

/**
 * 创建开发环境配置
 */
export function createDevelopmentConfig(overrides?: Partial<AgentConfig>): AgentConfig {
  return {
    ...DEFAULT_CONFIG,
    maxRetries: 5,
    retryDelay: 2000,
    ...overrides
  };
}

/**
 * 创建生产环境配置
 */
export function createProductionConfig(overrides?: Partial<AgentConfig>): AgentConfig {
  return {
    ...DEFAULT_CONFIG,
    maxRetries: 2,
    retryDelay: 500,
    ...overrides
  };
}

/**
 * 根据环境创建配置
 */
export function createConfigForEnvironment(
  env: 'development' | 'production' = 'development',
  overrides?: Partial<AgentConfig>
): AgentConfig {
  switch (env) {
    case 'development':
      return createDevelopmentConfig(overrides);
    case 'production':
      return createProductionConfig(overrides);
    default:
      return { ...DEFAULT_CONFIG, ...overrides };
  }
}

// 默认导出
export default DEFAULT_CONFIG;
