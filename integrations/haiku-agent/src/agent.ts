import { AbstractAgent, RunAgentInput, EventType, BaseEvent } from '@ag-ui/client';
import { Observable } from 'rxjs';
import type { AgentConfig, AgentState } from './types.js';
import { TopicExtractorTool } from './tools/topic-tool.js';
import { ContentGeneratorTool } from './tools/generate-tool.js';

export class Agent extends AbstractAgent {
  private config: AgentConfig;

  constructor(config?: Partial<AgentConfig>) {
    const fullConfig: AgentConfig = {
      threadId: config?.threadId,
      initialState: {
        items: [],
        currentItem: undefined,
        selectedIndex: -1,
        topic: '',
        isGenerating: false,
        history: [],
        preferences: {
          style: 'traditional',
          outputFormat: 'both',
          includeExtras: true
        },
        lastError: undefined,
        ...config?.initialState
      },
      ...config
    };

    super(fullConfig);
    this.config = fullConfig;
  }

  protected run(input: RunAgentInput): Observable<BaseEvent> {
    return new Observable<BaseEvent>((observer) => {
      // 发送运行开始事件
      observer.next({
        type: EventType.RUN_STARTED,
        timestamp: Date.now()
      });

      // 异步处理输入
      this.processInput(input)
        .then(() => {
          // 发送运行完成事件
          observer.next({
            type: EventType.RUN_FINISHED,
            timestamp: Date.now()
          });
          observer.complete();
        })
        .catch((error) => {
          // 发送运行错误事件
          observer.next({
            type: EventType.RUN_ERROR,
            timestamp: Date.now(),
            rawEvent: { error: error.message }
          });
          observer.error(error);
        });
    });
  }

  private async processInput(input: RunAgentInput): Promise<void> {
    const { messages } = input;

    // 获取最后一条用户消息
    const lastUserMessage = messages?.filter((msg) => msg.role === 'user')?.pop();

    if (!lastUserMessage?.content) {
      throw new Error('未找到用户消息');
    }

    const messageId = Date.now().toString();

    // 从消息中提取主题
    const topicTool = new TopicExtractorTool();
    const topic = await topicTool.execute(lastUserMessage.content);

    // 更新状态
    const currentState = this.state as AgentState;
    this.state = {
      ...currentState,
      topic,
      isGenerating: true
    };

    // 生成内容
    const generateTool = new ContentGeneratorTool();
    const result = await generateTool.execute({ topic });

    // 创建新项目对象
    const newItem = {
      primary: result.primary,
      secondary: result.secondary,
      extras: result.extras,
      metadata: result.metadata
    };

    // 使用新项目更新状态
    const updatedState = this.state as AgentState;
    const newItems = [...(updatedState.items || []), newItem];

    this.state = {
      ...updatedState,
      currentItem: newItem,
      items: newItems,
      selectedIndex: newItems.length - 1,
      history: [
        ...(updatedState.history || []),
        {
          topic,
          item: newItem,
          timestamp: Date.now()
        }
      ],
      isGenerating: false
    };

    // 将结果添加到消息中
    this.messages.push({
      id: messageId,
      role: 'assistant',
      content: `${newItem.primary}\n\n${newItem.secondary}`
    });
  }

  // 与代理交互的公共方法
  getCurrentItem(): any {
    const state = this.state as AgentState;
    return state?.currentItem || null;
  }

  getAllItems(): any[] {
    const state = this.state as AgentState;
    return state?.items || [];
  }

  getHistory(): AgentState['history'] {
    const state = this.state as AgentState;
    return state?.history || [];
  }

  updatePreferences(preferences: Partial<AgentState['preferences']>): void {
    const currentState = this.state as AgentState;
    if (currentState) {
      this.state = {
        ...currentState,
        preferences: {
          ...currentState.preferences,
          ...preferences
        }
      };
    }
  }

  clearHistory(): void {
    const currentState = this.state as AgentState;
    if (currentState) {
      this.state = {
        ...currentState,
        items: [],
        history: [],
        selectedIndex: -1,
        currentItem: undefined
      };
    }
  }
}

// 创建代理的工厂函数
export const createAgent = (config?: Partial<AgentConfig>): Agent => {
  return new Agent(config);
};
