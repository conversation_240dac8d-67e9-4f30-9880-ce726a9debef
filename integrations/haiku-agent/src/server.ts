/**
 * Haiku Agent - 服务器端入口
 * 只包含服务器端需要的功能：数据、配置、agent 创建函数
 * 不包含 React 组件
 */

import type { IntegrationPackage } from '@workspace/shared';
// import { createAgent } from './agent.js'; // 不再使用自定义Agent类
import { mastraAgent } from './mastra-agent.js'; // 使用Mastra Agent
import { DEFAULT_CONFIG } from './config.js';
import { topicExtractorTool } from './tools/topic-tool.js';
import { contentGeneratorTool } from './tools/generate-tool.js';

// 创建Agent的函数，返回Mastra Agent实例
export const createAgent = (config?: any) => {
  // 对于CopilotKit集成，我们需要返回Mastra Agent实例
  return mastraAgent;
};

// 服务器端 Integration 对象 - 不包含 Workspace 组件
export const HaikuServerIntegration = {
  // 基础信息
  id: 'haiku',
  name: 'Haiku Integration',
  description: '专业的诗词生成助手，能够创作优美的中英双语诗词',
  version: '0.0.1',

  // 核心功能
  createAgent,
  Agent: createAgent, // 别名

  // 配置
  defaultConfig: DEFAULT_CONFIG,

  // 工具
  tools: {
    topicExtractor: topicExtractorTool,
    contentGenerator: contentGeneratorTool
  },

  // 工具数组（用于注册）
  toolsArray: [topicExtractorTool, contentGeneratorTool]
};

// 创建服务器端 Integration Package - 不包含 workspace
export const haikuServerIntegrationPackage: IntegrationPackage = {
  id: HaikuServerIntegration.id,
  name: HaikuServerIntegration.name,
  description: HaikuServerIntegration.description,
  version: HaikuServerIntegration.version,
  createAgent: HaikuServerIntegration.createAgent,
  workspace: undefined as any, // 服务器端不需要 workspace 组件
  defaultConfig: HaikuServerIntegration.defaultConfig,
  tools: HaikuServerIntegration.toolsArray
};

// 默认导出服务器端集成
export default HaikuServerIntegration;

// 导出核心组件（服务器端）
export { DEFAULT_CONFIG } from './config.js';
export { topicExtractorTool } from './tools/topic-tool.js';
export { contentGeneratorTool } from './tools/generate-tool.js';
export { mastraAgent } from './mastra-agent.js';

// 导出类型
export type { AgentConfig, AgentState, Item, GenerateParams, GenerateResult } from './types.js';
