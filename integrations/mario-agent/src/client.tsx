'use client';

import React, { useState, useEffect } from 'react';
import type { WorkspaceProps, Item, AgentState } from './types.js';

// 简化的Workspace组件
export function Workspace({ isAgentActive, setIsAgentActive, lastMessage }: WorkspaceProps) {
  const [items, setItems] = useState<Item[]>([]);
  const [currentItem, setCurrentItem] = useState<Item | undefined>();
  const [isGenerating, setIsGenerating] = useState(false);

  // 模拟生成诗歌的功能
  const generateHaiku = async (topic: string) => {
    setIsGenerating(true);

    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 2000));

    const newItem: Item = {
      primary: `春风轻拂\n樱花飘落满地\n静谧无声`,
      secondary: `Spring wind blows\nCherry blossoms falling\nIn peaceful silence`,
      extras: ['自然', '宁静', '季节'],
      metadata: {
        topic,
        style: 'traditional',
        generatedAt: new Date().toISOString()
      }
    };

    setItems((prev) => [...prev, newItem]);
    setCurrentItem(newItem);
    setIsGenerating(false);
  };

  // 当有新消息时触发生成
  useEffect(() => {
    if (lastMessage && isAgentActive) {
      generateHaiku(lastMessage);
    }
  }, [lastMessage, isAgentActive]);

  return (
    <div className='mx-auto max-w-4xl p-6'>
      <div className='mb-6'>
        <h2 className='mb-2 text-2xl font-bold text-gray-800'>诗歌生成器</h2>
        <p className='text-gray-600'>输入主题，我将为您创作优美的中英双语诗歌</p>
      </div>

      {/* 状态指示器 */}
      <div className='mb-4 flex items-center gap-2'>
        <div className={`h-3 w-3 rounded-full ${isAgentActive ? 'bg-green-500' : 'bg-gray-300'}`} />
        <span className='text-sm text-gray-600'>{isAgentActive ? '活跃' : '非活跃'}</span>
        {isGenerating && <span className='ml-2 text-sm text-blue-600'>正在生成诗歌...</span>}
      </div>

      {/* 当前诗歌显示 */}
      {currentItem && (
        <div className='mb-6 rounded-lg border border-blue-200 bg-gradient-to-r from-blue-50 to-purple-50 p-6'>
          <h3 className='mb-4 text-lg font-semibold text-gray-800'>最新诗歌</h3>

          <div className='grid gap-6 md:grid-cols-2'>
            {/* 中文诗歌 */}
            <div className='rounded-lg bg-white p-4 shadow-sm'>
              <h4 className='mb-2 text-sm font-medium text-gray-600'>中文</h4>
              <div className='whitespace-pre-line text-lg font-medium leading-relaxed text-gray-800'>
                {currentItem.primary}
              </div>
            </div>

            {/* 英文诗歌 */}
            <div className='rounded-lg bg-white p-4 shadow-sm'>
              <h4 className='mb-2 text-sm font-medium text-gray-600'>English</h4>
              <div className='whitespace-pre-line text-lg font-medium leading-relaxed text-gray-800'>
                {currentItem.secondary}
              </div>
            </div>
          </div>

          {/* 标签 */}
          {currentItem.extras && currentItem.extras.length > 0 && (
            <div className='mt-4 border-t border-gray-200 pt-4'>
              <div className='flex flex-wrap gap-2'>
                {currentItem.extras.map((extra, index) => (
                  <span key={index} className='rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-800'>
                    {extra}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* 历史诗歌 */}
      {items.length > 1 && (
        <div className='rounded-lg border border-gray-200 bg-white'>
          <div className='border-b border-gray-200 p-4'>
            <h3 className='text-lg font-semibold text-gray-800'>历史诗歌</h3>
          </div>

          <div className='divide-y divide-gray-200'>
            {items
              .slice(0, -1)
              .reverse()
              .map((item, index) => (
                <div key={index} className='p-4 transition-colors hover:bg-gray-50'>
                  <div className='grid gap-4 md:grid-cols-2'>
                    <div className='whitespace-pre-line text-sm text-gray-800'>{item.primary}</div>
                    <div className='whitespace-pre-line text-sm text-gray-600'>{item.secondary}</div>
                  </div>

                  {item.extras && item.extras.length > 0 && (
                    <div className='mt-2 flex flex-wrap gap-1'>
                      {item.extras.map((extra, extraIndex) => (
                        <span key={extraIndex} className='rounded bg-gray-100 px-1.5 py-0.5 text-xs text-gray-600'>
                          {extra}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              ))}
          </div>
        </div>
      )}

      {/* 空状态 */}
      {items.length === 0 && !isGenerating && (
        <div className='py-12 text-center'>
          <div className='mb-2 text-gray-400'>
            <svg className='mx-auto h-16 w-16' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={1.5}
                d='M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253'
              />
            </svg>
          </div>
          <p className='text-gray-500'>还没有生成诗歌</p>
          <p className='mt-1 text-sm text-gray-400'>发送消息开始创作</p>
        </div>
      )}
    </div>
  );
}

// 重新导出服务端的内容
export * from './agent.js';
export * from './config.js';
