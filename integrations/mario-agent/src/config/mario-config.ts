import type { MarioConfig, ContainerConfig } from '../types/mario.js';

// 默认容器配置
const DEFAULT_CONTAINER_CONFIG: ContainerConfig = {
  image: 'node:18-alpine',
  name: 'mario-workspace',
  ports: [3000, 8080],
  environment: {
    NODE_ENV: 'development',
    PORT: '3000'
  },
  workingDir: '/workspace',
  command: ['sh', '-c', 'while true; do sleep 30; done']
};

// Mario Agent 配置
export const MARIO_CONFIG: MarioConfig = {
  // API 配置
  apiUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001',
  websocketUrl: process.env.NEXT_PUBLIC_MARIO_WS_URL || 'ws://localhost:8080/ws/ag-ui',
  dockerApiUrl: process.env.NEXT_PUBLIC_DOCKER_API_URL || 'http://localhost:3001/api/docker',

  // 默认模型和服务
  defaultModel: process.env.NEXT_PUBLIC_DEFAULT_MODEL || 'gpt-4',
  defaultMcpServers: ['filesystem', 'git', 'terminal'],

  // 容器默认配置
  containerDefaults: DEFAULT_CONTAINER_CONFIG,

  // 重试配置
  retryAttempts: 3,

  // 超时配置
  timeouts: {
    websocket: 30000, // 30秒
    container: 60000, // 60秒
    healthCheck: 30000 // 30秒
  }
};

// 环境配置
export const ENV_CONFIG = {
  isDevelopment: process.env.NODE_ENV === 'development',
  isProduction: process.env.NODE_ENV === 'production',
  isTest: process.env.NODE_ENV === 'test'
};

// Docker 镜像配置
export const DOCKER_IMAGES = {
  node: {
    '18': 'node:18-alpine',
    '20': 'node:20-alpine',
    latest: 'node:alpine'
  },
  python: {
    '3.9': 'python:3.9-slim',
    '3.10': 'python:3.10-slim',
    '3.11': 'python:3.11-slim',
    latest: 'python:3.11-slim'
  },
  ubuntu: {
    '20.04': 'ubuntu:20.04',
    '22.04': 'ubuntu:22.04',
    latest: 'ubuntu:22.04'
  }
};

// 端口配置
export const PORT_CONFIG = {
  // 常用开发端口
  development: [3000, 3001, 3002, 5000, 5001, 8000, 8080, 8081],
  // 生产环境端口
  production: [80, 443, 8080],
  // 端口范围
  range: {
    min: 3000,
    max: 9000
  }
};

// WebSocket 配置
export const WEBSOCKET_CONFIG = {
  // 重连配置
  reconnect: {
    enabled: true,
    delay: 3000,
    maxAttempts: 5,
    backoff: true
  },
  // 心跳配置
  heartbeat: {
    enabled: true,
    interval: 30000,
    timeout: 5000
  },
  // 消息队列配置
  messageQueue: {
    maxSize: 1000,
    flushInterval: 100
  }
};

// 终端配置
export const TERMINAL_CONFIG = {
  // 默认设置
  defaults: {
    fontSize: 14,
    fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
    theme: 'dark' as const,
    cursorBlink: true,
    scrollback: 1000,
    tabStopWidth: 4
  },
  // 主题配置
  themes: {
    dark: {
      background: '#1e1e1e',
      foreground: '#d4d4d4',
      cursor: '#d4d4d4',
      selection: '#264f78'
    },
    light: {
      background: '#ffffff',
      foreground: '#333333',
      cursor: '#333333',
      selection: '#add6ff'
    }
  }
};

// 文件操作配置
export const FILE_CONFIG = {
  // 支持的文件类型
  supportedTypes: [
    'txt',
    'md',
    'json',
    'js',
    'ts',
    'jsx',
    'tsx',
    'html',
    'css',
    'scss',
    'less',
    'py',
    'java',
    'cpp',
    'c',
    'go',
    'rs',
    'yml',
    'yaml',
    'xml',
    'toml',
    'sh',
    'bash',
    'zsh',
    'fish'
  ],
  // 最大文件大小 (bytes)
  maxFileSize: 10 * 1024 * 1024, // 10MB
  // 编码
  encoding: 'utf-8'
};

// 健康检查配置
export const HEALTH_CHECK_CONFIG = {
  // 检查间隔
  interval: 5000,
  // 超时时间
  timeout: 3000,
  // 重试次数
  retries: 3,
  // 检查路径
  paths: ['/', '/health', '/api/health', '/status']
};

// 日志配置
export const LOG_CONFIG = {
  // 日志级别
  level: ENV_CONFIG.isDevelopment ? 'debug' : 'info',
  // 最大日志条数
  maxEntries: 1000,
  // 日志格式
  format: {
    timestamp: true,
    level: true,
    source: true
  }
};

// 缓存配置
export const CACHE_CONFIG = {
  // 消息缓存
  messages: {
    maxSize: 500,
    ttl: 24 * 60 * 60 * 1000 // 24小时
  },
  // 容器信息缓存
  containers: {
    maxSize: 50,
    ttl: 60 * 60 * 1000 // 1小时
  },
  // 健康检查缓存
  healthCheck: {
    maxSize: 100,
    ttl: 5 * 60 * 1000 // 5分钟
  }
};

// UI 配置
export const UI_CONFIG = {
  // 动画配置
  animations: {
    enabled: true,
    duration: 200,
    easing: 'ease-in-out'
  },
  // 主题配置
  theme: {
    default: 'system',
    options: ['light', 'dark', 'system']
  },
  // 布局配置
  layout: {
    sidebar: {
      width: 300,
      collapsible: true
    },
    header: {
      height: 60,
      sticky: true
    }
  }
};

// 安全配置
export const SECURITY_CONFIG = {
  // CORS 配置
  cors: {
    enabled: true,
    origins: ENV_CONFIG.isDevelopment ? ['http://localhost:3000', 'http://localhost:3001'] : []
  },
  // CSP 配置
  csp: {
    enabled: ENV_CONFIG.isProduction,
    directives: {
      'default-src': ["'self'"],
      'script-src': ["'self'", "'unsafe-inline'"],
      'style-src': ["'self'", "'unsafe-inline'"],
      'img-src': ["'self'", 'data:', 'https:'],
      'connect-src': ["'self'", 'ws:', 'wss:']
    }
  }
};

// 性能配置
export const PERFORMANCE_CONFIG = {
  // 虚拟化配置
  virtualization: {
    enabled: true,
    itemHeight: 50,
    overscan: 5
  },
  // 懒加载配置
  lazyLoading: {
    enabled: true,
    threshold: 0.1,
    rootMargin: '50px'
  },
  // 防抖配置
  debounce: {
    search: 300,
    resize: 100,
    scroll: 16
  }
};

// 功能开关配置
export const FEATURE_FLAGS = {
  // 实验性功能
  experimental: {
    webgl: true,
    webAssembly: false,
    serviceWorker: false
  },
  // 调试功能
  debug: {
    enabled: ENV_CONFIG.isDevelopment,
    verbose: false,
    performance: false
  },
  // 分析功能
  analytics: {
    enabled: ENV_CONFIG.isProduction,
    trackErrors: true,
    trackPerformance: false
  }
};

// 导出所有配置
export const CONFIG = {
  mario: MARIO_CONFIG,
  env: ENV_CONFIG,
  docker: DOCKER_IMAGES,
  ports: PORT_CONFIG,
  websocket: WEBSOCKET_CONFIG,
  terminal: TERMINAL_CONFIG,
  files: FILE_CONFIG,
  healthCheck: HEALTH_CHECK_CONFIG,
  logs: LOG_CONFIG,
  cache: CACHE_CONFIG,
  ui: UI_CONFIG,
  security: SECURITY_CONFIG,
  performance: PERFORMANCE_CONFIG,
  features: FEATURE_FLAGS
};

// 配置验证函数
export function validateConfig(): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  // 验证必需的环境变量
  if (!MARIO_CONFIG.apiUrl) {
    errors.push('API URL is required');
  }

  if (!MARIO_CONFIG.websocketUrl) {
    errors.push('WebSocket URL is required');
  }

  if (!MARIO_CONFIG.dockerApiUrl) {
    errors.push('Docker API URL is required');
  }

  // 验证端口配置
  if (PORT_CONFIG.range.min >= PORT_CONFIG.range.max) {
    errors.push('Invalid port range configuration');
  }

  // 验证超时配置
  if (MARIO_CONFIG.timeouts.websocket <= 0) {
    errors.push('WebSocket timeout must be positive');
  }

  if (MARIO_CONFIG.timeouts.container <= 0) {
    errors.push('Container timeout must be positive');
  }

  if (MARIO_CONFIG.timeouts.healthCheck <= 0) {
    errors.push('Health check timeout must be positive');
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

// 获取运行时配置
export function getRuntimeConfig() {
  return {
    ...CONFIG,
    runtime: {
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : '',
      platform: typeof navigator !== 'undefined' ? navigator.platform : '',
      language: typeof navigator !== 'undefined' ? navigator.language : 'en',
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      timestamp: new Date().toISOString()
    }
  };
}

export default CONFIG;
