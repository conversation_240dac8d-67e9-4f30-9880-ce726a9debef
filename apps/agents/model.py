
from langchain_openai import ChatOpenAI
from langchain_anthropic import ChatAnthropic
from typing import Optional
from settings.settings import logger

_openai_llm_instance = None
_claude_llm_instance = None


def create_openai_llm(
        model: str,
        base_url: Optional[str] = None,
        api_key: Optional[str] = None,
        temperature: float = 0.0,
        **kwargs,
) -> ChatOpenAI:
    """
    初始化openai模型
    """
    llm_kwargs = {"model": model, "temperature": temperature, **kwargs}

    if base_url:
        llm_kwargs["base_url"] = base_url

    if api_key:
        llm_kwargs["api_key"] = api_key

    return ChatOpenAI(**llm_kwargs)


def create_anthropic_llm(
        model: str,
        base_url: Optional[str] = None,
        api_key: Optional[str] = None,
        temperature: float = 0.0,
        max_tokens: int = 10000,
        **kwargs,
) -> ChatAnthropic:
    """
    初始化claude模型
    """
    llm_kwargs = {
        "model": model,
        "temperature": temperature,
        "max_tokens": max_tokens,
        **kwargs
    }

    if base_url:
        llm_kwargs["base_url"] = base_url

    if api_key:
        llm_kwargs["api_key"] = api_key
        # 添加默认的认证头
        llm_kwargs["default_headers"] = {"Authorization": f"Bearer {api_key}"}

    return ChatAnthropic(**llm_kwargs)


def get_llm_by_type():
    """
    获取OpenAI模型实例（单例模式）
    """
    global _openai_llm_instance

    if _openai_llm_instance is None:
        llm_config = {
            "model": "gpt-4o-mini",
            "base_url": "https://aigc.sankuai.com/v1/openai/native",
            "api_key": "1914304559263223873"
        }
        try:
            _openai_llm_instance = create_openai_llm(**llm_config)
            logger.info("OpenAI模型实例初始化成功")
        except Exception as e:
            raise ValueError(f"获取OpenAI大模型失败: {str(e)}")

    return _openai_llm_instance


def get_llm_coder_type():
    """
    获取Claude编码模型实例（单例模式），优先使用3.7版本，失败时回退到3.5版本
    """
    global _claude_llm_instance

    if _claude_llm_instance is None:
        config = {
            "temperature": 0,
            "max_tokens": 30000,
            "timeout": 60,  # 设置60秒超时
            "api_key": "1914304559263223873",
            "base_url": "https://aigc.sankuai.com/v1/claude/stream",
            "default_headers": {"Authorization": "Bearer 1914304559263223873"}
        }

        try:
            logger.info("尝试初始化Claude 3.7模型...")
            _claude_llm_instance = ChatAnthropic(model="anthropic.claude-3.7-sonnet", **config)

            # 测试模型是否可用
            test_response = _claude_llm_instance.invoke([{"role": "user", "content": "测试"}])
            if test_response and hasattr(test_response, 'content') and test_response.content:
                logger.info("Claude 3.7模型实例初始化并测试成功")
            else:
                raise Exception("Claude 3.7模型测试失败：响应为空")

        except Exception as e:
            logger.error(f"Claude 3.7模型初始化失败, 回退到3.5: {str(e)}")
            try:
                logger.info("尝试初始化Claude 3.5模型...")
                _claude_llm_instance = ChatAnthropic(model="anthropic.claude-3.5-sonnet", **config)

                # 测试模型是否可用
                test_response = _claude_llm_instance.invoke([{"role": "user", "content": "测试"}])
                if test_response and hasattr(test_response, 'content') and test_response.content:
                    logger.info("Claude 3.5模型实例初始化并测试成功")
                else:
                    raise Exception("Claude 3.5模型测试失败：响应为空")

            except Exception as e2:
                logger.error(f"Claude 3.5模型初始化也失败: {str(e2)}")
                raise ValueError(f"所有Claude模型初始化失败: 3.7错误={str(e)}, 3.5错误={str(e2)}")

    return _claude_llm_instance


if __name__ == "__main__":
    llm = get_llm_coder_type()
    invoke = llm.invoke("不允许省略，我需要你用java写一个完整的红黑树")
    # # 使用 stream=True 获取流式响应
    # for chunk in llm.stream("不允许省略，我需要你用java写一个完整的红黑树"):
    #     # 打印每个响应片段的内容
    #     if chunk.content:
    #         print(chunk.content, end="", flush=True)
    print(invoke)  # 最后打印换行




