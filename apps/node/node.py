
import time
import json
import re
import traceback

from apps.agents.agents import administrator_agent, coder_agent, markdown_agent
from apps.agents.model import get_llm_by_type, get_llm_coder_type
from langgraph.types import Command, interrupt
from typing import Literal

from apps.node.state_types import State, UserInfo
from apps.prompts.template import apply_prompt_template, prompt_origin_template
from apps.service.code_service import convert_ec_to_markdown, make_uuid_dir, generate_markdown_file
from langchain_core.messages import HumanMessage
from settings.settings import logger
from pycat import Cat
RESPONSE_FORMAT = "Response from {}:\n\n<response>\n{}\n</response>\n\n*Please execute the next step.*"

Cat.init_cat("com.sankuai.toolchain.chatops.app", disable_falcon=True)


def judge_node(state: State) -> Command[Literal["planner", "__end__"]]:
    logger.info("Judge node starting task")
    logger.info(f"Judge node received state: {state}")

    t = Cat.new_transaction("MarioAgent", "Judge")

    try:
        # 检查状态中的消息
        messages = state.get("messages", [])
        logger.info(f"Judge node messages count: {len(messages)}")

        if not messages:
            logger.warning("Judge node: No messages found in state")
            return Command(goto="__end__")

        # 获取最后一条用户消息
        user_messages = [msg for msg in messages if hasattr(msg, 'content') and msg.content.strip()]
        if not user_messages:
            logger.warning("Judge node: No valid user messages found")
            return Command(goto="__end__")

        logger.info(f"Judge node: Found {len(user_messages)} valid messages")

        # 应用提示模板
        template = apply_prompt_template("judge", state)
        logger.info(f"Judge node template: {template}")

        if not template or not any(msg.get("content", "").strip() for msg in template):
            logger.error("Judge node: Template is empty or invalid")
            return Command(goto="__end__")

        # 调用LLM
        logger.info("Judge node: Calling LLM...")
        llm = get_llm_coder_type()
        response = llm.invoke(template)

        if not response or not hasattr(response, 'content'):
            logger.error("Judge node: LLM response is empty or invalid")
            return Command(goto="__end__")

        response_content = response.content
        logger.info(f"Judge node response: {response_content}")

        if not response_content or not response_content.strip():
            logger.error("Judge node: LLM response content is empty")
            return Command(goto="__end__")

        mode = None
        if "Ec_Exist()" in response_content:
            mode = "EC"
        elif "Ec_NoExist()" in response_content:
            mode = "NoEC"

        goto = "planner" if mode else "__end__"

        logger.info(f"Judge node decision - goto: {goto}, mode: {mode}")
        return Command(
            update={
                "messages": [HumanMessage(content=response_content, name="judge")],
                "mode": mode
            },
            goto=goto,
        )
    except Exception as e:
        logger.error(f"Judge node failed with error: {e}")
        logger.error(f"Judge node error details: {traceback.format_exc()}")
        Cat.log_error("judge", traceback.format_exc())
        # 发生错误时返回默认行为
        return Command(
            goto="__end__",
        )
    finally:
        t.complete()


def planner_node(state: State) -> Command[Literal["generator", "__end__"]]:
    """Planner node that generate the full plan."""
    t = Cat.new_transaction("MarioAgent", "Planner")
    full_response = ""
    try:
        logger.info("Planner generating full plan")
        messages = apply_prompt_template("planner", state)
        # whether to enable deep thinking mode
        llm = get_llm_by_type()
        if state.get("deep_thinking_mode"):
            llm = get_llm_by_type()
        stream = llm.stream(messages)
        for chunk in stream:
            full_response += chunk.content
        logger.info(f"Current state messages: {state['messages']}")
        logger.info(f"Planner response: {full_response}")

        if full_response.startswith("```json"):
            full_response = full_response.removeprefix("```json")

        if full_response.endswith("```"):
            full_response = full_response.removesuffix("```")
        json.loads(full_response)
        goto = "generator"
    except Exception as e:
        logger.error(f"Planner node failed with error: {e}")
        Cat.log_error("Planner", traceback.format_exc())
        goto = "__end__"
    finally:
        t.complete()
    return Command(
        update={
            "messages": [HumanMessage(content=full_response, name="planner")],
            "full_plan": full_response,
        },
        goto=goto
    )


def generator_node(state: State) -> Command[Literal["corrector", "__end__"]]:
    logger.info("Generator node starting task")
    t = Cat.new_transaction("MarioAgent", "Generator")
    try:
        # 不管是什么模式，都需要解析用户输入并提取关键信息
        template_user_input = prompt_origin_template("generator_user_input")
        llm = get_llm_by_type()
        human_message = state.get("messages")[0].content
        input_message = llm.invoke(template_user_input + [{"role": "user", "content": human_message}])

        format_input_message = json.loads(input_message.content)
        ec_id = format_input_message.get("ec_id")
        user_mis = format_input_message.get("user_mis")

        # 打点用户信息
        Cat.log_event("Muse", user_mis)

        mode = state.get("mode", "")
        template = prompt_origin_template("generator")

        if mode == "EC":
            ec_content = convert_ec_to_markdown(ec_id, user_mis)
            response = get_llm_coder_type().invoke(template + [{"role": "user", "content": ec_content}])
        else:
            human_message = state.get("messages")[0].content
            response = get_llm_coder_type().invoke(template + [{"role": "user", "content": human_message}])

        response_content = response.content
        is_full = False
        if (response_content.startswith("完整的测试用例文档如下") or
                "完整的测试用例文档如下" in response_content or
                "完整的正则" in response_content):
            is_full = True

        logger.info(f"Generator node response: {response.content}")
        return Command(
            update={
                "user_info": UserInfo(
                    test_case_id=ec_id,
                    user_mis=user_mis
                ).to_dict(),
                "messages": [HumanMessage(content=response_content, name="generator")],
                "markdown_content": response_content,
                "is_full": is_full,
                "corrector_attempt_count": 0,
                "user_input": state.get("messages")[0].content
            },
            goto="corrector",
        )
    except Exception as e:
        logger.error(f"generator node failed with error: {e}")
        Cat.log_error("Generator", traceback.format_exc())
        return Command(
            goto="__end__"
        )
    finally:
        t.complete()


def corrector_node(state: State) -> Command[Literal["corrector", "repo_corrector", "__end__"]]:
    logger.info("Correct node starting task")
    max_attempts = 5
    response_content = ""
    # 检查is_full状态
    if not state.get("is_full", False):
        # 检查是否超过最大尝试次数
        if state.get("corrector_attempt_count", 0) >= max_attempts:
            logger.warning(f"Reached maximum attempts ({max_attempts}), ending process")
            return Command(goto="__end__")

        logger.info("Information is not complete, requesting user to supplement")

        # 增加尝试次数
        current_attempt = state.get("corrector_attempt_count", 0) + 1
        state["corrector_attempt_count"] = current_attempt

        # 获取用户的原始输入
        original_input = state.get("user_input")

        interrupt_message = state.get("messages")[-1].content if state.get("messages") else ""
        supplement_input = interrupt({
            "message": f"{interrupt_message}\n\n请提供补充信息: ",
            "type": "input_required"
        })

        # 获取generator的prompt模板
        template = prompt_origin_template("generator")

        # 组合原始输入和补充输入作为用户消息
        combined_input = f"原始输入：{original_input}\n\n补充信息：{supplement_input}"

        # 构建完整的消息列表
        messages = template + [{"role": "user", "content": combined_input}]

        # 调用LLM生成内容
        llm = get_llm_coder_type()
        response = llm.invoke(messages)
        response_content = response.content

        is_full = any([
            response_content.startswith("完整的测试用例文档如下"),
            "完整的测试用例文档如下" in response_content,
            "完整的正则" in response_content
        ])

        logger.info("Information supplemented and content generated")

        # 如果信息仍不完整，返回自身节点继续循环
        # 更新状态并根据is_full决定下一步
        return Command(
            update={
                "corrector_attempt_count": current_attempt,
                "is_full": is_full,
                "user_input": combined_input,
                "markdown_content": response_content,
                "messages": [HumanMessage(content=response_content, name="corrector")]
            },
            goto="repo_corrector" if is_full else "corrector"
        )
    return Command(
        update={
            "corrector_attempt_count": 0,
            "is_full": True,
            "user_input": state.get("messages")[0].content,
            "messages": [HumanMessage(content=response_content, name="corrector")]
        },
        goto="repo_corrector"
    )


def repo_detail_node(state: State) -> Command[Literal["administrator", "__end__"]]:
    logger.info("Repo detail node starting task")

    # 最多允许2次输入机会
    max_attempts = 2
    current_attempt = 0

    while current_attempt < max_attempts:
        current_attempt += 1

        if current_attempt == 1:
            message = "现在需要您提供自动化测试仓库以及分支信息供后续代码生成到对应仓库中\n\n请提供以下信息：\n1. 仓库地址（repository）\n2. 分支名称（branch）"
        else:
            message = f"您提供的信息不完整，请重新提供：\n1. 仓库地址（repository）\n2. 分支名称（branch）"

        value = interrupt(
            {
                "message": message,
                "type": "repo_detail_required"
            }
        )

        # 获取用户输入
        user_input = value
        logger.info(f"User input received: {user_input}")

        # 使用大模型分析用户输入
        llm = get_llm_by_type()
        analysis_prompt = f"""请分析以下用户输入，提取仓库地址和分支信息。
                
                用户输入：{user_input}
                
                请按照以下JSON格式返回结果：
                {{
                    "repository": "提取到的仓库地址，如果没有则为空字符串",
                    "branch": "提取到的分支名称，如果没有则为空字符串",
                    "has_repo": true/false,
                    "has_branch": true/false,
                }}
                
                注意：
                1. 仓库地址可能是HTTP/HTTPS URL、SSH URL或简单的用户名/仓库名格式
                2. 分支名称可能包含master、main、develop、dev、feature/xxx、release/xxx等
                3. 用户可能使用中文描述，如"分支"、"仓库"等词汇
                4. 如果信息不明确，confidence设为low
                5. 请务必返回有效的JSON格式"""

        messages = [HumanMessage(content=analysis_prompt)]
        response = llm.invoke(messages)
        analysis_result = response.content

        # 解析JSON响应
        has_repo = False
        has_branch = False
        repo_info = ''
        branch_info = ''

        # 提取JSON部分
        json_match = re.search(r'\{.*\}', analysis_result, re.DOTALL)
        if json_match:
            json_str = json_match.group()
            try:
                parsed_result = json.loads(json_str)
                has_repo = parsed_result.get('has_repo', False)
                has_branch = parsed_result.get('has_branch', False)
                repo_info = parsed_result.get('repository', '').strip()
                branch_info = parsed_result.get('branch', '').strip()

            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse LLM JSON response: {str(e)}")
                logger.error(f"Raw response: {analysis_result}")
        else:
            logger.error("No JSON found in LLM response")
            logger.error(f"Raw response: {analysis_result}")

        # 如果两个信息都有，询问用户确认
        if has_repo and has_branch:
            confirm_message = f"检测到以下信息：\n仓库: {repo_info}\n分支: {branch_info}\n\n请确认信息是否正确？"
            confirm_value = interrupt(
                {
                    "message": confirm_message,
                    "type": "repo_detail_confirm"
                }
            )
            # 判断用户回复是否是肯定的语义
            confirm_prompt = """请分析用户的回复是否表达了确认或同意的意思：
            用户回复：'{user_response}'

            判断标准：
            - 肯定词汇：是、好、可以、确认、同意、没问题、OK、对的、正确等
            - 肯定表达：看起来不错、没有问题、就这样等
            - 否定或修改意图：不、错了、需要改、修改、不对等

            如果表达确认同意，请回复"confirmed"；如果表达否定或需要修改，请回复"modify"。
            只返回"confirmed"或"modify"，无需其他内容。
            """
            confirmation_result = llm.invoke(confirm_prompt.format(user_response=confirm_value))

            if confirmation_result.content.strip().lower() == "confirmed":
                # 更新state中的用户信息
                user_info = state["user_info"]
                user_info["repository"] = repo_info
                user_info["branch"] = branch_info
                return Command(
                    update={
                        "user_info": user_info
                    },
                    goto="administrator"
                )
            else:
                # 用户不确认，继续循环
                continue

        # 如果信息不完整且还有机会，继续循环
        if current_attempt < max_attempts:
            continue
        else:
            # 超过最大尝试次数，使用默认值或结束
            break

    # 如果最终还是没有完整信息，返回错误或使用默认值
    error_message = "未能获取到完整的仓库和分支信息，请重新开始流程。"
    logger.info(error_message)

    return Command(
        update={
            "user_info": UserInfo()  # 使用默认空值
        },
        goto="__end__"
    )


def markdown_node(state: State) -> Command[Literal["human_node"]]:
    logger.info("Markdown agent starting task")
    t = Cat.new_transaction("MarioAgent", "Markdown")
    try:
        uuid_dir = make_uuid_dir()

        ec_content = state.get("markdown_content",  "")

        generate_markdown_file(uuid_dir, ec_content)

        admin_state = {
            "messages": [{
                "role": "user",
                "content":  f"文件夹路径uuid_dir为：{uuid_dir}"
            }]
        }

        result = markdown_agent.invoke(admin_state)

        logger.info("Markdown agent completed task")
        logger.info(f"Markdown agent response: {result['messages'][-1].content}")

        uuid_str = ""
        case_type = ""

        content_dict = json.loads(result["messages"][-1].content)

        if isinstance(content_dict, dict):
            folder_path = content_dict.get("folder_path", "")
            case_type = content_dict.get("case_type", "")

            if folder_path:
                uuid_str = folder_path.split("/")[-1]

        return Command(
            update={
                "case_type": case_type,
                "uuid_str": uuid_str,
                "messages": [
                    HumanMessage(
                        content=RESPONSE_FORMAT.format(
                            "markdownor", result["messages"][-1].content
                        ),
                        name="markdownor",
                    )
                ]
            },
            goto="human_node",
        )
    except Exception as e:
        logger.error(f"markdown node failed with error: {e}")
        Cat.log_error("Markdown", traceback.format_exc())
        return Command(
            goto="__end__"
        )
    finally:
        t.complete()


def coder_node(state: State) -> Command[Literal["reporter"]]:

    logger.info("Coder agent starting task")
    t = Cat.new_transaction("MarioAgent", "Coder")

    logger.info("Coder agent waiting for 30 seconds...")
    time.sleep(30)
    logger.info("Coder agent resumed after 30 seconds delay")

    user_info = state.get("user_info", {})
    try:
        uuid_dir = state.get("uuid_str", "")
        port = state.get("port", "")
        case_type = state.get("case_type", "")
        admin_state = {
            "messages": [{
                "role": "user",
                "content": (
                    f"仓库地址repo为：{user_info.get('repository', '')}，"
                    f"对应路径uuid_dir为: {uuid_dir}, "
                    f"对应端口port为: {port}, "
                    f"用例类型case_type为: {case_type}"
                )
            }]
        }
        result = coder_agent.invoke(admin_state)
        logger.info("Coder agent completed task")
        logger.info(f"Coder agent response: {result['messages'][-1].content}")
        return Command(
            update={
                "mario_case": result['messages'][-1].content,
                "messages": [
                    HumanMessage(
                        content=RESPONSE_FORMAT.format(
                            "coder", result["messages"][-1].content
                        ),
                        name="coder",
                    )
                ]
            },
            goto="reporter",
        )
    except Exception as e:
        logger.error(f"Coder node failed with error: {e}")
        Cat.log_error("Coder", traceback.format_exc())
        return Command(
            goto="__end__"
        )
    finally:
        t.complete()


def administrator_node(state: State) -> Command[Literal["markdownor", "__end__"]]:
    logger.info("Administrator agent starting task")
    t = Cat.new_transaction("MarioAgent", "Administrator")
    try:
        user_info = state.get("user_info", {})

        # 创建一个新的state副本，将user_info中的必要字段放在顶层
        admin_state = {"messages": [{"role": "user", "content": f"仓库地址为：{user_info.get('repository', '')}，"
                                                                f"用户信息为: {user_info.get('user_mis', '')}"}]}
        result = administrator_agent.invoke(admin_state)
        logger.info(f"Administrator agent response: {result['messages'][-1].content}")
        # 判断权限，决定下一步去向
        goto = "markdownor" if "有权限" in result['messages'][-1].content else "__end__"
        return Command(
            update={
                "messages": [
                    HumanMessage(
                        content=RESPONSE_FORMAT.format(
                            "administrator", result["messages"][-1].content
                        ),
                        name="administrator",
                    )
                ]
            },
            goto=goto,
        )
    except Exception as e:
        logger.error(f"Administrator node failed with error: {e}")
        Cat.log_error("Administrator", traceback.format_exc())
        return Command(
            goto="__end__"
        )
    finally:
        t.complete()


def reporter_node(state: State) -> Command[Literal["__end__"]]:
    logger.info("Reporter write final report")
    t = Cat.new_transaction("MarioAgent", "Reporter")

    try:
        messages = apply_prompt_template("reporter", state)
        response = get_llm_by_type().invoke(messages)
        logger.info(f"Current state messages: {state['messages']}")
        logger.info(f"reporter response: {response}")

        return Command(
            update={
                "messages": [
                    HumanMessage(
                        content=RESPONSE_FORMAT.format("reporter", response.content),
                        name="reporter",
                    )
                ]
            },
            goto="__end__",
        )
    except Exception as e:
        logger.error(f"Reporter node failed with error: {e}")
        Cat.log_error("Reporter", traceback.format_exc())
        return Command(
            goto="__end__"
        )
    finally:
        t.complete()


def human_node(state: State) -> Command:
    value = interrupt(
        {
            "message": "以上是为您生成的代码预览，请确认是否符合您的需求。如果符合，代码将自动导入右侧编译器环境；如不符合，将结束此次代码生成任务。",
            "type": "approve"
        }
    )
    if value.get('input') == "是":
        return Command(
            update={
                "port": value.get('port')
            },
            goto="coder"
        )
    else:
        return Command(goto="__end__")


# if __name__ == "__main__":
    # content = "通过Pigeon泛化调用方式访问服务名称为com.sankuai.dztheme.dealproduct.DealProductService、服务AppKey为com.sankuai.dztheme.dealgroup的query方法，planId(String)、productIds(List<Integer>)、extParams(Map<String, Object>)作为请求参数，deals(List<DealProductDTO>)作为响应结果生成用例。"
    # template = prompt_origin_template("generator_user_input")
    # res = template + [{"role": "user", "content": content}]
    # llm = get_llm_by_type()
    # result = llm.invoke(res)
    # print(result.content)
    # template = prompt_origin_template("generator") + [content]
    # response = get_llm_coder_type1().invoke(template)
    # result = get_llm_coder_type1().invoke(content + prompt)
    # logger.info(response.content)
