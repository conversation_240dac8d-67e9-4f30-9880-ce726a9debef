#!/usr/bin/python3
# coding: utf-8


import uuid

from apps.node.gragh import build_graph
from settings.settings import logger
from langgraph.types import Command, Interrupt
from fastapi import WebSocket

graph = build_graph()

# 定义主图节点列表，与 build_graph 中的节点保持一致
MAIN_GRAPH_NODES = ["human_input", "human_node", "judge", "corrector", "generator", "coordinator", "planner", "administrator", "markdownor",
                    "coder", "reporter", "repo_corrector"]


async def run_agent_workflow(
    user_input_messages: list,
    deep_thinking_mode: bool = False,
    search_before_planning: bool = False,
    websocket: WebSocket = None,  # 添加可选的WebSocket参数
):
    if not user_input_messages:
        raise ValueError("Input could not be empty")

    # 验证消息格式和内容
    valid_messages = []
    for i, msg in enumerate(user_input_messages):
        if not isinstance(msg, dict):
            logger.warning(f"消息 {i} 不是字典格式: {type(msg)}")
            continue

        content = msg.get("content", "").strip()
        if not content:
            logger.warning(f"消息 {i} 内容为空: {msg}")
            continue

        valid_messages.append(msg)

    if not valid_messages:
        raise ValueError("没有找到有效的消息内容")

    logger.info(f"Starting workflow with {len(valid_messages)} valid messages: {valid_messages}")
    workflow_id = str(uuid.uuid4())

    # 记录当前活跃的节点
    active_nodes = set()
    # 定义允许返回模型回复的节点列表
    allowed_model_response_nodes = ["planner", "generator", "corrector", "judge", "reporter"]
    # 跟踪节点的开始和结束状态
    node_status = {node: {"started": False, "completed": False} for node in MAIN_GRAPH_NODES}

    # 发送工作流开始事件
    yield {
        "event": "workflow_status",
        "status": "started",
        "workflow_id": workflow_id
    }
    config = {
        "configurable": {
            "thread_id": str(uuid.uuid4()),  # 使用唯一ID
        }
    }

    # 初始输入
    current_input = {
        # Constants
        "TEAM_MEMBERS": [],
        # Runtime Variables
        "messages": valid_messages,  # 使用验证过的消息
        "deep_thinking_mode": deep_thinking_mode,
        "search_before_planning": search_before_planning,
    }

    # 跟踪是否需要继续执行
    continue_execution = True

    while continue_execution:
        continue_execution = False  # 默认不继续，除非遇到中断

        async for event in graph.astream_events(
                current_input,
                config=config,
                version="v2",
        ):
            # 处理中断事件
            chunk = event.get("data", {}).get("chunk")
            if isinstance(chunk, dict) and "__interrupt__" in chunk:
                interrupt_data = chunk["__interrupt__"]

                # 提取中断信息
                if isinstance(interrupt_data, tuple):
                    interrupt_obj = interrupt_data[0]
                else:
                    interrupt_obj = interrupt_data

                # 确保是Interrupt类型
                if isinstance(interrupt_obj, Interrupt):
                    message = interrupt_obj.value["message"]
                    interrupt_type = interrupt_obj.value["type"]
                    # 根据WebSocket是否存在选择不同的输入方式
                    if websocket:
                        # 通过WebSocket发送中断消息并等待用户响应
                        await websocket.send_json({
                            'type': 'interrupt',
                            'message': message,
                            'interrupt_type': interrupt_type
                        })
                        # 循环接收消息，直到收到非心跳消息
                        while True:
                            response = await websocket.receive_json()
                            if response.get("type", "") != "heartbeat":
                                break
                        if interrupt_type == "approve":
                            current_input = Command(resume=response)
                        else:
                            user_input = response.get("input", "")
                            current_input = Command(resume=user_input)
                    else:
                        response = input(f"Interrupt: {message}\nResponse: ")
                        current_input = Command(resume=response)

                    # 标记需要继续执行
                    continue_execution = True

                    # 跳出当前循环，使用新的输入重新开始
                    break

            # 处理其他事件类型
            kind = event.get("event")
            metadata = event.get("metadata", {})
            # 必须用这个metadata.get("langgraph_node", "")调用工具的时候节点名称会显示tools
            langgraph_node = (
                ""
                if (metadata.get("langgraph_checkpoint_ns") is None)
                else metadata.get("langgraph_checkpoint_ns").split(":")[0]
            )
            # # 只处理主图节点的事件
            if langgraph_node not in MAIN_GRAPH_NODES:
                continue
            # 处理节点开始事件
            if kind == "on_chain_start":
                if not node_status[langgraph_node]["started"]:
                    node_status[langgraph_node]["started"] = True
                    active_nodes.add(langgraph_node)
                    # 发送节点开始事件
                    yield {
                        "event": "node_status",
                        "status": "started",
                        "node": langgraph_node
                    }
            # 处理模型回复事件
            elif kind == "on_chat_model_end":
                # 只处理允许列表中的节点
                if langgraph_node in allowed_model_response_nodes:

                    # 提取模型输出内容
                    data = event.get("data", {})
                    output = data.get("output")
                    if output:
                        if langgraph_node == "generator" or "corrector":
                            if (output.content.startswith("完整的测试用例文档如下") or
                                    "完整的测试用例文档如下" in output.content or
                                    "完整的正则" in output.content):
                                yield {
                                    "event": "model_response",
                                    "node": langgraph_node,
                                    "content": output.content,
                                    "model_name": output.response_metadata.get("model_name", "unknown"),
                                    "tokens": output.usage_metadata.get("total_tokens", 0)
                                    if hasattr(output, "usage_metadata") else 0
                                }
                        if langgraph_node == "judge":
                            if "Ec_Exist()" not in output.content and "Ec_NoExist()" not in output.content:
                                yield {
                                    "event": "model_response",
                                    "node": langgraph_node,
                                    "content": output.content,
                                    "model_name": output.response_metadata.get("model_name", "unknown"),
                                    "tokens": output.usage_metadata.get("total_tokens", 0)
                                    if hasattr(output, "usage_metadata") else 0
                                }
                        if langgraph_node == "planner":
                            # 发送模型回复事件
                            yield {
                                "event": "model_response",
                                "node": langgraph_node,
                                "content": output.content,
                                "model_name": output.response_metadata.get("model_name", "unknown"),
                                "tokens": output.usage_metadata.get("total_tokens", 0)
                                if hasattr(output, "usage_metadata") else 0
                            }
            elif kind == "on_tool_start":
                data = event.get("data", {})
                yield {
                    "event": "tool_call_result",
                    "node": langgraph_node,
                    "status": "started",
                    "tool_result": data.get("input"),
                }
            elif kind == "on_tool_end":
                data = event.get("data", {})
                yield {
                    "event": "tool_call_result",
                    "node": langgraph_node,
                    "status": "completed",
                    "tool_result": data["output"].content if data.get("output") else "",
                }

            # 处理节点结束事件
            elif kind == "on_chain_end":
                if langgraph_node in active_nodes and not node_status[langgraph_node]["completed"]:
                    node_status[langgraph_node]["completed"] = True
                    active_nodes.remove(langgraph_node)
                    # 发送节点结束事件
                    yield {
                        "event": "node_chain_end",
                        "status": "completed",
                        "node": langgraph_node,
                        "input_data": event.get("data", {}).get("input", {}),
                        "output_data": event.get("data", {}).get("output", {})
                    }


    # 发送工作流结束事件
    yield {
        "event": "workflow_status",
        "status": "completed",
        "workflow_id": workflow_id
    }




