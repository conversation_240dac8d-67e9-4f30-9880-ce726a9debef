'use client';

import { usePathname } from 'next/navigation';
import React, { createContext, useContext, useState, useEffect, ReactNode, useRef } from 'react';
import { useAgents, AgentInfo, AgentConfig } from '@/hooks/use-agent-mode';

// Define the shape of an agent. Adjust as needed for your app.
export type Agent = AgentInfo & { config: AgentConfig };

// Context value type
type AgentContextType = {
  currentAgent: Agent | null;
  setAgent: (agent: Agent) => void;
  agents: Agent[];
  setAgents: (agents: Agent[]) => void;
  loading: boolean;
  error: string | null;
};

const AgentContext = createContext<AgentContextType | undefined>(undefined);

export const AgentProvider = ({ children }: { children: ReactNode }) => {
  const { agents, loading, error } = useAgents();
  const pathname = usePathname();
  const [currentAgent, setCurrentAgent] = useState<Agent | null>(null);
  const initializedRef = useRef(false);

  // 根据当前路径和可用agents设置默认agent
  useEffect(() => {
    if (agents.length > 0 && !initializedRef.current) {
      const pathSegment = pathname?.split('/')[1] || '';

      // 根据路径匹配agent
      const matchedAgent = agents.find((agent) => {
        return agent.route === `/${pathSegment}`;
      });

      // 如果找到匹配的agent，设置为当前agent，否则使用第一个
      const selectedAgent = matchedAgent || agents[0] || null;
      setCurrentAgent(selectedAgent);
      initializedRef.current = true;

      console.log('Agent initialized:', selectedAgent?.id, 'for path:', pathname);
    }
  }, [agents, pathname]);

  // 当路径变化时，更新当前agent（但不重置初始化标记）
  useEffect(() => {
    if (agents.length > 0 && initializedRef.current) {
      const pathSegment = pathname?.split('/')[1] || '';

      // 根据路径匹配agent
      const matchedAgent = agents.find((agent) => {
        return agent.route === `/${pathSegment}`;
      });

      // 只有当找到不同的匹配agent时才更新
      if (matchedAgent && matchedAgent.id !== currentAgent?.id) {
        setCurrentAgent(matchedAgent);
        console.log('Agent changed due to path change:', matchedAgent.id, 'for path:', pathname);
      }
    }
  }, [pathname, agents, currentAgent?.id]);

  const setAgent = (agent: Agent) => {
    setCurrentAgent(agent);
    console.log('Agent manually set to:', agent.id);
  };

  const setAgents = (newAgents: Agent[]) => {
    // 这个方法保留用于向后兼容，但实际上 agents 由 hook 管理
    console.warn('setAgents is deprecated, agents are now managed by useAgents hook');
  };

  return (
    <AgentContext.Provider
      value={{
        currentAgent,
        setAgent,
        agents,
        setAgents,
        loading,
        error
      }}>
      {children}
    </AgentContext.Provider>
  );
};

export const useAgent = () => {
  const context = useContext(AgentContext);
  if (!context) {
    throw new Error('useAgent must be used within an AgentProvider');
  }
  return context;
};
