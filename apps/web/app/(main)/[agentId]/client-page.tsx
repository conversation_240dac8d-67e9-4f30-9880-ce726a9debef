'use client';

import { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useUserAuth } from '@/hooks/use-user-auth';
import { AuthLoadingTerminal } from '@workspace/ui/components/loading-terminal';
import { useAgentSession } from '@/hooks/use-agent-mode';

interface AgentClientPageProps {
  agentId: string;
  agentName: string;
}

/**
 * 客户端 Agent 页面组件
 * 自动创建新会话并重定向到带UUID的URL
 */
export default function AgentClientPage({ agentId, agentName }: AgentClientPageProps) {
  const router = useRouter();
  const { isLoggedIn, username, isLoading } = useUserAuth();
  const [isCreatingSession, setIsCreatingSession] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const { displayName, agentConfig } = useAgentSession(agentId);

  // 会话创建函数，支持重试
  const createNewSession = useCallback(async () => {
    // 等待认证状态确定
    if (isLoading) {
      return;
    }

    // 检查用户是否已登录
    if (!isLoggedIn) {
      console.error(`用户未登录，无法创建${displayName}会话`);
      router.replace('/');
      return;
    }

    // 检查必要的配置是否存在
    if (!agentConfig) {
      console.error('Agent配置未加载');
      return;
    }

    try {
      // 清除之前的错误状态
      setError(null);
      setIsCreatingSession(true);

      // 创建新会话
      const response = await fetch('/api/agent-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          agentId,
          agentName: displayName,
          title: `${displayName}对话 - ${new Date().toLocaleString('zh-CN')}`,
          username: username || '',
          mode: agentConfig.integrationType,
          agentConfig: agentConfig.customConfig
        })
      });

      if (response.ok) {
        const data = await response.json();
        // 成功创建会话，重置重试计数器并重定向
        setRetryCount(0);
        router.replace(`/${agentId}/${data.sessionId}`);
        return;
      }

      // 处理HTTP错误
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `创建${displayName}会话失败`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      console.error(`创建${displayName}会话时发生错误:`, errorMessage);

      setIsCreatingSession(false);
      setError(errorMessage);

      // 重试逻辑：最多重试3次
      if (retryCount < 3) {
        console.log(`准备进行第${retryCount + 1}次重试...`);
        setTimeout(
          () => {
            setRetryCount((prev) => prev + 1);
          },
          1000 * (retryCount + 1)
        ); // 递增延迟：1s, 2s, 3s
      } else {
        console.error(`创建${displayName}会话失败，已达到最大重试次数`);
        setError(`创建${displayName}会话失败，请刷新页面重试`);
      }
    }
  }, [agentId, displayName, router, isLoggedIn, username, isLoading, agentConfig, retryCount]);

  // 统一的会话创建effect
  useEffect(() => {
    // 只有在认证状态确定且用户已登录时才尝试创建会话
    if (!isLoading && isLoggedIn && agentConfig) {
      createNewSession();
    }
  }, [createNewSession, isLoading, isLoggedIn, agentConfig]);

  // 显示加载状态
  return <AuthLoadingTerminal agentName={displayName} isLoading={isLoading} isCreatingSession={isCreatingSession} />;
}
