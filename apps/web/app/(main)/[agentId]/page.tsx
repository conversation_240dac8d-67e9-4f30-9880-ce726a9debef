import React from 'react';
import { getIntegrationList } from '@workspace/agent-registry/server';
import { notFound } from 'next/navigation';
import AgentClientPage from './client-page';

/**
 * 生成静态参数，用于构建时预渲染所有可用的 agent 页面
 */
export async function generateStaticParams() {
  try {
    const integrations = await getIntegrationList();
    console.log('generateStaticParams - integrations:', integrations);

    if (!integrations || integrations.length === 0) {
      console.warn('No integrations found, returning default agents');
      return [{ agentId: 'haiku' }, { agentId: 'mario' }];
    }

    return integrations.map((integration) => ({
      agentId: integration.id
    }));
  } catch (error) {
    console.error('Error in generateStaticParams:', error);
    return [{ agentId: 'haiku' }, { agentId: 'mario' }];
  }
}

interface AgentPageProps {
  params: Promise<{
    agentId: string;
  }>;
}

/**
 * 服务端 Agent 页面组件
 * 验证 agentId 并渲染客户端组件
 */
export default async function AgentPage({ params }: AgentPageProps) {
  const { agentId } = await params;

  // 检查 agent 是否存在
  const allIntegrations = await getIntegrationList();
  const agentExists = allIntegrations.some((integration) => integration.id === agentId);

  if (!agentExists) {
    notFound();
  }

  // 获取agent信息
  const agentInfo = allIntegrations.find((integration) => integration.id === agentId);
  const agentName = agentInfo?.name || `${agentId.charAt(0).toUpperCase() + agentId.slice(1)} Agent`;

  return <AgentClientPage agentId={agentId} agentName={agentName} />;
}
