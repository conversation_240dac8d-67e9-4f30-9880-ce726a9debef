'use client';

import { useState, useEffect } from 'react';
import { AppLayout } from '@/components/layout/app-layout';
import { AgentProvider } from '@/components/providers/agent-provider';
import { CopilotKit, useCopilotMessagesContext } from '@copilotkit/react-core';
import '@copilotkit/react-ui/styles.css';
import type { AgentSession, AgentMessage } from '@/lib/db/schema';
import { Role, TextMessage } from '@copilotkit/runtime-client-gql';
import { SessionLoadingTerminal } from '@workspace/ui/components/loading-terminal';
import { useAgentSession } from '@/hooks/use-agent-mode';

interface AgentSessionClientPageProps {
  agentId: string;
  agentName: string;
  sessionId: string;
  session: AgentSession;
}

/**
 * 带会话ID的客户端 Agent 页面组件
 * 处理特定会话的CopilotKit逻辑并加载历史消息
 * 支持 Mario 模式和常规 CopilotKit 模式
 */
export default function AgentSessionClientPage({
  agentId,
  agentName,
  sessionId,
  session
}: AgentSessionClientPageProps) {
  const [messages, setMessages] = useState<AgentMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const { isSpecialMode, sessionTitle, agentConfig } = useAgentSession(agentId, sessionId);

  useEffect(() => {
    const loadMessages = async () => {
      try {
        const response = await fetch(`/api/agent-message?sessionId=${sessionId}`);
        if (response.ok) {
          const data = await response.json();
          setMessages(data.messages || []);
        } else {
          console.error(`Failed to load ${agentConfig.displayName} messages`);
        }
      } catch (error) {
        console.error(`Error loading ${agentConfig.displayName} messages:`, error);
      } finally {
        setLoading(false);
      }
    };

    loadMessages();
  }, [sessionId, agentConfig.displayName]);

  if (loading) {
    return <SessionLoadingTerminal sessionTitle={sessionTitle(session.title)} />;
  }

  return (
    <AgentProvider>
      <CopilotKitWrapper
        agentId={agentId}
        agentName={agentName}
        sessionId={sessionId}
        session={session}
        messages={messages}
      />
    </AgentProvider>
  );
}

/**
 * CopilotKit 包装器组件
 * 智能选择 API 路由，支持 Mario 模式和常规模式
 */
function CopilotKitWrapper({
  agentId,
  agentName,
  sessionId,
  session,
  messages
}: {
  agentId: string;
  agentName: string;
  sessionId: string;
  session: AgentSession;
  messages: AgentMessage[];
}) {
  const { apiRoute } = useAgentSession(agentId, sessionId);

  return (
    <CopilotKit runtimeUrl={apiRoute!} agent={agentId}>
      <MessageLoader messages={messages} />
      <AppLayout sessionId={sessionId} session={session} />
    </CopilotKit>
  );
}

/**
 * 消息加载器组件
 * 使用 useCopilotMessagesContext 来设置历史消息
 */
function MessageLoader({ messages }: { messages: AgentMessage[] }) {
  const { setMessages } = useCopilotMessagesContext();

  useEffect(() => {
    if (messages.length > 0) {
      // 将数据库消息转换为 CopilotKit 消息格式
      const copilotMessages = messages.map(
        (msg) =>
          new TextMessage({
            id: msg.id,
            role: msg.role === 'user' ? Role.User : Role.Assistant,
            content: msg.content,
            createdAt: msg.createdAt
          })
      );
      setMessages(copilotMessages);
    }
  }, [messages, setMessages]);

  return null; // 这个组件不渲染任何内容
}
