#!/usr/bin/python3
# coding: utf-8
"""
AG-UI兼容的API端点
实现AG-UI协议标准的WebSocket和HTTP端点
"""

import json
import uuid
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, HTTPException
from pydantic import BaseModel
import logging
logger = logging.getLogger(__name__)
from .ag_ui_adapter import AGUIAdapter


router = APIRouter()


class AGUIRunRequest(BaseModel):
    """AG-UI运行请求模型"""
    messages: List[Dict[str, Any]]
    thread_id: Optional[str] = None
    run_id: Optional[str] = None
    tools: Optional[List[Dict[str, Any]]] = None
    state: Optional[Dict[str, Any]] = None


class AGUIMessage(BaseModel):
    """AG-UI消息模型"""
    id: str
    role: str
    content: Optional[str] = None
    name: Optional[str] = None
    tool_calls: Optional[List[Dict[str, Any]]] = None
    tool_call_id: Optional[str] = None


@router.websocket("/ws/ag-ui")
async def ag_ui_websocket_endpoint(websocket: WebSocket):
    """
    AG-UI兼容的WebSocket端点

    支持AG-UI协议的实时通信，包括：
    - 流式消息传输
    - 状态同步
    - 工具调用
    - 中断处理
    """
    await websocket.accept()
    adapter = None

    try:
        logger.info("AG-UI WebSocket连接已建立")

        while True:
            # 接收客户端消息
            try:
                data = await websocket.receive_text()
                request_data = json.loads(data)
                logger.info(f"收到AG-UI请求: {request_data}")

                # 处理不同类型的请求
                request_type = request_data.get("type", "run")

                if request_type == "run":
                    # 处理运行请求
                    await _handle_run_request(websocket, request_data)

                elif request_type == "interrupt_response":
                    # 处理中断响应
                    await _handle_interrupt_response(websocket, request_data)

                elif request_type == "heartbeat":
                    # 处理心跳
                    await websocket.send_text(json.dumps({
                        "type": "heartbeat_ack",
                        "timestamp": request_data.get("timestamp")
                    }))

                else:
                    logger.warning(f"未知的请求类型: {request_type}")
                    await websocket.send_text(json.dumps({
                        "type": "error",
                        "error": f"未知的请求类型: {request_type}"
                    }))

            except json.JSONDecodeError as e:
                logger.error(f"JSON解析错误: {e}")
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "error": "无效的JSON格式"
                }))

    except WebSocketDisconnect:
        logger.info("AG-UI WebSocket连接已断开")
    except Exception as e:
        logger.error(f"AG-UI WebSocket错误: {e}", exc_info=True)
        try:
            await websocket.send_text(json.dumps({
                "type": "error",
                "error": str(e)
            }))
        except:
            pass
        await websocket.close()


@router.websocket("/ws/ag-ui/info")
async def ag_ui_websocket_info_endpoint(websocket: WebSocket):
    """
    AG-UI WebSocket信息端点

    CopilotKit会访问这个端点来获取agent信息
    """
    await websocket.accept()

    try:
        logger.info("AG-UI WebSocket Info连接已建立")

        # 发送agent信息
        info_response = {
            "name": "LangGraph AG-UI Adapter",
            "description": "将LangGraph工作流适配为AG-UI协议的服务",
            "version": "1.0.0",
            "protocol_version": "ag-ui-1.0",
            "endpoints": {
                "websocket": "/ws/ag-ui",
                "http": "/ag-ui/run",
                "health": "/ag-ui/health",
                "info": "/ag-ui/info"
            },
            "supported_events": [
                "run_started",
                "run_finished",
                "text_message_start",
                "text_message_delta",
                "text_message_end",
                "tool_calls_start",
                "tool_calls_args",
                "tool_calls_end",
                "state_snapshot",
                "state_delta",
                "messages_snapshot",
                "error",
                "interrupt"
            ],
            "features": {
                "streaming": True,
                "state_management": True,
                "tool_calls": True,
                "interrupts": True,
                "multi_agent": True,
                "human_in_loop": True
            }
        }

        await websocket.send_text(json.dumps(info_response))

        # 保持连接，等待客户端断开
        while True:
            try:
                await websocket.receive_text()
            except WebSocketDisconnect:
                break

    except WebSocketDisconnect:
        logger.info("AG-UI WebSocket Info连接已断开")
    except Exception as e:
        logger.error(f"AG-UI WebSocket Info错误: {e}", exc_info=True)
        await websocket.close()


async def _handle_run_request(websocket: WebSocket, request_data: Dict[str, Any]):
    """
    处理运行请求

    Args:
        websocket: WebSocket连接
        request_data: 请求数据
    """
    try:
        # 提取请求参数
        messages = request_data.get("messages", [])
        thread_id = request_data.get("thread_id")
        run_id = request_data.get("run_id")
        tools = request_data.get("tools", [])
        state = request_data.get("state", {})

        logger.info(f"收到AG-UI运行请求 - thread_id: {thread_id}, run_id: {run_id}")
        logger.info(f"消息数量: {len(messages)}, 消息内容: {messages}")

        # 验证消息列表
        if not messages:
            error_msg = "消息列表不能为空"
            logger.error(error_msg)
            await websocket.send_text(json.dumps({
                "type": "error",
                "error": error_msg
            }))
            return

        # 验证消息内容
        valid_messages = []
        for i, msg in enumerate(messages):
            if not isinstance(msg, dict):
                logger.warning(f"消息 {i} 不是字典格式: {type(msg)}")
                continue

            content = msg.get("content", "").strip()
            if not content:
                logger.warning(f"消息 {i} 内容为空: {msg}")
                continue

            valid_messages.append(msg)

        if not valid_messages:
            error_msg = "没有找到有效的消息内容"
            logger.error(error_msg)
            await websocket.send_text(json.dumps({
                "type": "error",
                "error": error_msg
            }))
            return

        logger.info(f"有效消息数量: {len(valid_messages)}")

        # 创建适配器实例
        adapter = AGUIAdapter(thread_id=thread_id, run_id=run_id)

        # 转换消息格式
        user_input_messages = []
        for msg in valid_messages:
            user_input_messages.append({
                "role": msg.get("role", "user"),
                "content": msg.get("content", "").strip()
            })

        logger.info(f"转换后的用户输入消息: {user_input_messages}")

        # 流式处理并发送AG-UI事件
        async for agui_event in adapter.convert_workflow_events_to_agui(
            user_input_messages=user_input_messages,
            websocket=websocket
        ):
            await websocket.send_text(json.dumps(agui_event))

    except Exception as e:
        logger.error(f"处理运行请求失败: {e}", exc_info=True)
        await websocket.send_text(json.dumps({
            "type": "error",
            "error": str(e)
        }))


async def _handle_interrupt_response(websocket: WebSocket, request_data: Dict[str, Any]):
    """
    处理中断响应

    Args:
        websocket: WebSocket连接
        request_data: 请求数据
    """
    # 这里可以实现中断响应的处理逻辑
    # 目前简单确认收到响应
    await websocket.send_text(json.dumps({
        "type": "interrupt_ack",
        "response": request_data.get("response")
    }))


@router.post("/ag-ui/run")
async def ag_ui_http_endpoint(request: AGUIRunRequest):
    """
    AG-UI兼容的HTTP端点

    支持通过HTTP POST请求运行工作流，返回完整的事件流

    Args:
        request: AG-UI运行请求

    Returns:
        包含所有事件的响应
    """
    try:
        logger.info(f"收到AG-UI HTTP请求: {request}")

        # 创建适配器实例
        adapter = AGUIAdapter(
            thread_id=request.thread_id,
            run_id=request.run_id
        )

        # 转换消息格式
        user_input_messages = []
        for msg in request.messages:
            user_input_messages.append({
                "role": msg.get("role", "user"),
                "content": msg.get("content", "")
            })

        # 收集所有事件
        events = []
        async for agui_event in adapter.convert_workflow_events_to_agui(
            user_input_messages=user_input_messages
        ):
            events.append(agui_event)

        return {
            "success": True,
            "thread_id": adapter.thread_id,
            "run_id": adapter.run_id,
            "events": events,
            "final_state": adapter.state,
            "messages": adapter.messages
        }

    except Exception as e:
        logger.error(f"AG-UI HTTP请求处理失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/ag-ui/health")
async def ag_ui_health_check():
    """
    AG-UI健康检查端点

    Returns:
        服务状态信息
    """
    return {
        "status": "healthy",
        "service": "ag-ui-langgraph-adapter",
        "version": "1.0.0",
        "protocol": "ag-ui",
        "capabilities": [
            "streaming",
            "state_management",
            "tool_calls",
            "interrupts",
            "websocket",
            "http"
        ]
    }


@router.get("/ag-ui/info")
async def ag_ui_info():
    """
    AG-UI服务信息端点

    Returns:
        详细的服务信息
    """
    return {
        "name": "LangGraph AG-UI Adapter",
        "description": "将LangGraph工作流适配为AG-UI协议的服务",
        "version": "1.0.0",
        "protocol_version": "ag-ui-1.0",
        "endpoints": {
            "websocket": "/ws/ag-ui",
            "http": "/ag-ui/run",
            "health": "/ag-ui/health",
            "info": "/ag-ui/info"
        },
        "supported_events": [
            "run_started",
            "run_finished",
            "text_message_start",
            "text_message_delta",
            "text_message_end",
            "tool_calls_start",
            "tool_calls_args",
            "tool_calls_end",
            "state_snapshot",
            "state_delta",
            "messages_snapshot",
            "error",
            "interrupt"
        ],
        "features": {
            "streaming": True,
            "state_management": True,
            "tool_calls": True,
            "interrupts": True,
            "multi_agent": True,
            "human_in_loop": True
        }
    }
